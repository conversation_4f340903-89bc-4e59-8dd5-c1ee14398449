# -*- coding: utf-8 -*-
"""
BCC功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_bcc_validation():
    """测试BCC邮箱验证功能"""
    print("=" * 50)
    print("测试BCC邮箱验证功能...")
    
    try:
        from utils.config import validate_bcc_email
        
        # 测试有效的BCC格式
        valid_bcc_emails = [
            "<EMAIL>",
            "Kell yan <<EMAIL>>",
            "User Name <<EMAIL>>",
            "<EMAIL>"
        ]
        
        # 测试无效的BCC格式
        invalid_bcc_emails = [
            "invalid",
            "Name <invalid-email>",
            "<>",
            "Name <>",
            ""
        ]
        
        print("测试有效BCC格式:")
        for email in valid_bcc_emails:
            result = validate_bcc_email(email)
            status = "✓" if result else "✗"
            print(f"  {status} {email}")
            if not result:
                print(f"    错误：应该是有效的BCC格式")
                return False
        
        print("\n测试无效BCC格式:")
        for email in invalid_bcc_emails:
            result = validate_bcc_email(email)
            status = "✓" if not result else "✗"
            print(f"  {status} {email}")
            if result:
                print(f"    错误：应该是无效的BCC格式")
                return False
        
        print("✓ BCC邮箱验证功能正常")
        return True
        
    except Exception as e:
        print(f"✗ BCC邮箱验证测试失败: {e}")
        return False


def test_gui_bcc_components():
    """测试GUI BCC组件"""
    print("\n" + "=" * 50)
    print("测试GUI BCC组件...")
    
    try:
        # 导入PyQt
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用程序（不显示）
        app = QApplication(sys.argv)
        
        # 导入GUI组件
        from gui.components import EmailInputWidget
        
        # 创建邮件输入组件
        widget = EmailInputWidget()
        
        # 测试BCC配置获取
        bcc_config = widget.get_bcc_config()
        print(f"默认BCC配置: {bcc_config}")
        
        # 测试BCC配置设置
        widget.set_bcc_config(True, "<EMAIL>")
        new_config = widget.get_bcc_config()
        print(f"设置后BCC配置: {new_config}")
        
        if new_config["enabled"] and new_config["address"] == "<EMAIL>":
            print("✓ BCC配置功能正常")
        else:
            print("✗ BCC配置功能异常")
            return False
        
        # 清理
        widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI BCC组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_email_sender_bcc():
    """测试邮件发送模块的BCC功能"""
    print("\n" + "=" * 50)
    print("测试邮件发送模块BCC功能...")
    
    try:
        from utils.email_sender import check_outlook_availability
        
        # 检查Outlook可用性
        available, message = check_outlook_availability()
        if not available:
            print(f"⚠️  Outlook不可用，跳过邮件发送测试: {message}")
            return True
        
        print("✓ Outlook可用，BCC功能集成正常")
        print("  注意：实际邮件发送测试需要手动进行")
        
        return True
        
    except Exception as e:
        print(f"✗ 邮件发送模块BCC测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Betelligent邮件发送工具 - BCC功能测试")
    print("=" * 50)
    
    tests = [
        ("BCC邮箱验证", test_bcc_validation),
        ("GUI BCC组件", test_gui_bcc_components),
        ("邮件发送BCC", test_email_sender_bcc),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✓ {test_name} 测试通过")
            else:
                print(f"\n✗ {test_name} 测试失败")
        except Exception as e:
            print(f"\n✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"BCC功能测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有BCC功能测试通过！")
        return 0
    else:
        print("⚠️  部分BCC功能测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
