#!/usr/bin/env python3
"""
Betelligent邮件发送工具 - 主程序入口

这是一个基于PyQt的GUI应用程序，用于通过Outlook发送HTML格式的通知邮件。

功能特性:
- 通过Outlook COM接口发送邮件
- 支持HTML邮件模板和依赖文件
- 记录发送历史和操作日志
- 用户友好的图形界面

使用方法:
    python main.py

环境要求:
- Windows操作系统
- Python 3.7+
- PyQt5或PyQt6
- Microsoft Outlook
- win32com.client (pywin32)

作者: Betelligent Team
版本: 1.0.0
"""

import sys
import os
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 检查必要的依赖
def check_dependencies():
    """检查必要的依赖库"""
    missing_deps = []
    
    # 检查PyQt
    try:
        import PyQt5
    except ImportError:
        try:
            import PyQt6
        except ImportError:
            missing_deps.append("PyQt5 或 PyQt6")
    
    # 检查win32com
    try:
        import win32com.client
    except ImportError:
        missing_deps.append("pywin32 (win32com)")
    
    if missing_deps:
        print("错误: 缺少必要的依赖库:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请安装缺少的依赖:")
        print("pip install PyQt5 pywin32")
        print("或")
        print("pip install PyQt6 pywin32")
        return False
    
    return True


def check_environment():
    """检查运行环境"""
    # 检查操作系统
    if sys.platform != "win32":
        print("警告: 此程序设计为在Windows系统上运行")
        print("某些功能（如Outlook集成）可能无法正常工作")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        return False
    
    return True


def setup_logging():
    """设置日志系统"""
    try:
        from utils.logger import setup_logger
        from utils.config import create_required_directories
        
        # 创建必要的目录
        create_required_directories()
        
        # 设置日志
        logger = setup_logger()
        logger.info("Application starting...")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Platform: {sys.platform}")
        
        return logger
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        return None


def main():
    """主函数"""
    print("Betelligent邮件发送工具 v1.0.0")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        input("按回车键退出...")
        return 1
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return 1
    
    # 设置日志
    logger = setup_logging()
    
    try:
        # 导入GUI模块
        from gui.main_window import run_application
        
        print("正在启动应用程序...")
        
        # 运行应用程序
        run_application()
        
    except ImportError as e:
        error_msg = f"模块导入失败: {e}"
        print(f"错误: {error_msg}")
        if logger:
            logger.error(error_msg)
        input("按回车键退出...")
        return 1
        
    except Exception as e:
        error_msg = f"应用程序运行时出现错误: {e}"
        print(f"错误: {error_msg}")
        print("\n详细错误信息:")
        traceback.print_exc()
        
        if logger:
            logger.error(error_msg)
            logger.error(traceback.format_exc())
        
        input("按回车键退出...")
        return 1
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n程序异常退出: {e}")
        traceback.print_exc()
        input("按回车键退出...")
        sys.exit(1)
