#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能测试脚本

测试邮件发送工具的各个核心功能模块。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("=" * 50)
    print("测试模块导入...")
    
    try:
        from utils.logger import setup_logger, get_logger
        print("✓ 日志模块导入成功")
        
        from utils.config import validate_email, get_config
        print("✓ 配置模块导入成功")
        
        from utils.file_handler import read_html_file, get_dependency_files
        print("✓ 文件处理模块导入成功")
        
        from utils.email_sender import check_outlook_availability
        print("✓ 邮件发送模块导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False


def test_logger():
    """测试日志系统"""
    print("\n" + "=" * 50)
    print("测试日志系统...")
    
    try:
        from utils.logger import setup_logger, get_logger
        
        # 设置日志
        logger = setup_logger()
        print("✓ 日志系统初始化成功")
        
        # 测试日志记录
        logger.info("测试信息日志")
        logger.warning("测试警告日志")
        print("✓ 日志记录功能正常")
        
        return True
    except Exception as e:
        print(f"✗ 日志系统测试失败: {e}")
        return False


def test_config():
    """测试配置管理"""
    print("\n" + "=" * 50)
    print("测试配置管理...")
    
    try:
        from utils.config import validate_email, get_config, get_email_subject
        
        # 测试邮箱验证
        valid_emails = ["<EMAIL>", "<EMAIL>"]
        invalid_emails = ["invalid", "@domain.com", "user@"]
        
        for email in valid_emails:
            if not validate_email(email):
                print(f"✗ 有效邮箱验证失败: {email}")
                return False
        print("✓ 有效邮箱验证通过")
        
        for email in invalid_emails:
            if validate_email(email):
                print(f"✗ 无效邮箱验证失败: {email}")
                return False
        print("✓ 无效邮箱验证通过")
        
        # 测试配置获取
        config = get_config("email")
        if not config:
            print("✗ 配置获取失败")
            return False
        print("✓ 配置获取成功")
        
        # 测试邮件主题
        subject = get_email_subject()
        if subject != "Notification for Betelligent account is activated":
            print(f"✗ 邮件主题不正确: {subject}")
            return False
        print("✓ 邮件主题配置正确")
        
        return True
    except Exception as e:
        print(f"✗ 配置管理测试失败: {e}")
        return False


def test_file_handler():
    """测试文件处理"""
    print("\n" + "=" * 50)
    print("测试文件处理...")
    
    try:
        from utils.file_handler import (
            read_html_file, get_dependency_files, 
            validate_file_exists, prepare_email_attachments
        )
        
        # 测试HTML文件读取
        html_content = read_html_file()
        if not html_content:
            print("✗ HTML文件读取失败")
            return False
        print(f"✓ HTML文件读取成功 (长度: {len(html_content)})")
        
        # 测试依赖文件获取
        dependencies = get_dependency_files()
        print(f"✓ 依赖文件获取成功 (数量: {len(dependencies)})")
        
        # 测试文件存在性验证
        html_file = "Notification for Betelligent account is activated.htm"
        if not validate_file_exists(html_file):
            print(f"✗ HTML文件不存在: {html_file}")
            return False
        print("✓ HTML文件存在性验证通过")
        
        # 测试邮件附件准备
        html, attachments = prepare_email_attachments()
        if not html:
            print("✗ 邮件附件准备失败")
            return False
        print(f"✓ 邮件附件准备成功 (附件数量: {len(attachments)})")
        
        return True
    except Exception as e:
        print(f"✗ 文件处理测试失败: {e}")
        return False


def test_outlook_integration():
    """测试Outlook集成"""
    print("\n" + "=" * 50)
    print("测试Outlook集成...")
    
    try:
        from utils.email_sender import check_outlook_availability, create_outlook_application
        
        # 检查Outlook可用性
        available, message = check_outlook_availability()
        if not available:
            print(f"✗ Outlook不可用: {message}")
            return False
        print("✓ Outlook可用性检查通过")
        
        # 测试Outlook应用程序创建
        outlook_app = create_outlook_application()
        if not outlook_app:
            print("✗ Outlook应用程序创建失败")
            return False
        print("✓ Outlook应用程序创建成功")
        
        return True
    except Exception as e:
        print(f"✗ Outlook集成测试失败: {e}")
        return False


def test_gui_imports():
    """测试GUI模块导入"""
    print("\n" + "=" * 50)
    print("测试GUI模块导入...")
    
    try:
        # 测试PyQt可用性
        try:
            import PyQt5
            print("✓ PyQt5可用")
        except ImportError:
            try:
                import PyQt6
                print("✓ PyQt6可用")
            except ImportError:
                print("✗ PyQt不可用")
                return False
        
        # 测试GUI组件导入
        from gui.components import EmailInputWidget, HistoryTableWidget, StatusBarWidget
        print("✓ GUI组件导入成功")
        
        from gui.main_window import MainWindow
        print("✓ 主窗口导入成功")
        
        return True
    except Exception as e:
        print(f"✗ GUI模块导入失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Betelligent邮件发送工具 - 功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("日志系统", test_logger),
        ("配置管理", test_config),
        ("文件处理", test_file_handler),
        ("Outlook集成", test_outlook_integration),
        ("GUI模块", test_gui_imports),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✓ {test_name} 测试通过")
            else:
                print(f"\n✗ {test_name} 测试失败")
        except Exception as e:
            print(f"\n✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序可以正常运行。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
