"""
日志系统模块

提供统一的日志记录功能，支持文件日志和控制台日志。
采用函数式编程设计，提供简洁的日志接口。
"""

import logging
import os
from datetime import datetime
from typing import Optional


def setup_logger(
    name: str = "betelligent_email", 
    log_level: str = "INFO",
    log_dir: str = "data/logs",
    console_output: bool = True
) -> logging.Logger:
    """
    设置并配置日志记录器
    
    Args:
        name: 日志记录器名称
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: 日志文件目录
        console_output: 是否输出到控制台
        
    Returns:
        配置好的日志记录器
    """
    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 文件处理器 - 按日期分割日志文件
    today = datetime.now().strftime('%Y%m%d')
    log_file = os.path.join(log_dir, f"email_sender_{today}.log")
    
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 控制台处理器
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger


def get_logger(name: str = "betelligent_email") -> logging.Logger:
    """
    获取已配置的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器实例
    """
    logger = logging.getLogger(name)
    if not logger.handlers:
        # 如果没有配置过，使用默认配置
        return setup_logger(name)
    return logger


def log_email_operation(
    operation: str,
    email_address: str,
    status: str,
    message: str = "",
    logger_name: str = "betelligent_email"
) -> None:
    """
    记录邮件操作日志
    
    Args:
        operation: 操作类型 (send_email, validate_email, etc.)
        email_address: 邮箱地址
        status: 操作状态 (success, error, warning)
        message: 详细信息
        logger_name: 日志记录器名称
    """
    logger = get_logger(logger_name)
    
    log_message = f"Operation: {operation} | Email: {email_address} | Status: {status}"
    if message:
        log_message += f" | Message: {message}"
    
    if status.lower() == "success":
        logger.info(log_message)
    elif status.lower() == "warning":
        logger.warning(log_message)
    elif status.lower() == "error":
        logger.error(log_message)
    else:
        logger.info(log_message)


def log_file_operation(
    operation: str,
    file_path: str,
    status: str,
    message: str = "",
    logger_name: str = "betelligent_email"
) -> None:
    """
    记录文件操作日志
    
    Args:
        operation: 操作类型 (read_file, process_html, etc.)
        file_path: 文件路径
        status: 操作状态 (success, error, warning)
        message: 详细信息
        logger_name: 日志记录器名称
    """
    logger = get_logger(logger_name)
    
    log_message = f"Operation: {operation} | File: {file_path} | Status: {status}"
    if message:
        log_message += f" | Message: {message}"
    
    if status.lower() == "success":
        logger.info(log_message)
    elif status.lower() == "warning":
        logger.warning(log_message)
    elif status.lower() == "error":
        logger.error(log_message)
    else:
        logger.info(log_message)


def cleanup_old_logs(log_dir: str = "data/logs", days_to_keep: int = 30) -> None:
    """
    清理旧的日志文件
    
    Args:
        log_dir: 日志文件目录
        days_to_keep: 保留天数
    """
    if not os.path.exists(log_dir):
        return
    
    logger = get_logger()
    current_time = datetime.now()
    
    try:
        for filename in os.listdir(log_dir):
            if filename.endswith('.log'):
                file_path = os.path.join(log_dir, filename)
                file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                
                if (current_time - file_time).days > days_to_keep:
                    os.remove(file_path)
                    logger.info(f"Cleaned up old log file: {filename}")
                    
    except Exception as e:
        logger.error(f"Error cleaning up logs: {str(e)}")


# 模块级别的默认日志记录器
_default_logger = None


def get_default_logger() -> logging.Logger:
    """获取默认的日志记录器"""
    global _default_logger
    if _default_logger is None:
        _default_logger = setup_logger()
    return _default_logger
