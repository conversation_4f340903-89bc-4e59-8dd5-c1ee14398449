# -*- coding: utf-8 -*-
"""
GUI测试脚本 - 测试GUI组件是否能正常创建
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui_creation():
    """测试GUI组件创建"""
    try:
        # 导入PyQt
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # 创建应用程序（不显示）
        app = QApplication(sys.argv)
        
        # 导入GUI组件
        from gui.components import EmailInputWidget, HistoryTableWidget, StatusBarWidget
        from gui.main_window import MainWindow
        
        print("GUI组件导入成功")
        
        # 创建主窗口（但不显示）
        window = MainWindow()
        print("主窗口创建成功")
        
        # 测试基本功能
        print(f"窗口标题: {window.windowTitle()}")
        print(f"窗口大小: {window.size().width()}x{window.size().height()}")
        
        # 清理
        window.close()
        app.quit()
        
        print("GUI测试完成 - 所有组件正常")
        return True
        
    except Exception as e:
        print(f"GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gui_creation()
    sys.exit(0 if success else 1)
