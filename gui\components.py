"""
GUI组件模块

包含各种UI组件的实现，如邮件输入框、历史记录表格等。
"""

import sys
from typing import List, Dict, Any, Optional
from datetime import datetime

try:
    from PyQt5.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
        QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
        QStatusBar, QMessageBox, QApplication, QFrame, QCheckBox,
        QGroupBox
    )
    from PyQt5.QtCore import Qt, pyqtSignal, QTimer
    from PyQt5.QtGui import QFont, QPalette, QColor
    PYQT_AVAILABLE = True
except ImportError:
    try:
        from PyQt6.QtWidgets import (
            QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
            QPushButton, QTableWidget, QTableWidgetItem, QHeader<PERSON>iew,
            QStatusBar, QMessageBox, QApplication, QFrame, QCheckBox,
            QGroupBox
        )
        from PyQt6.QtCore import Qt, pyqtSignal, QTimer
        from PyQt6.QtGui import QFont, QPalette, QColor
        PYQT_AVAILABLE = True
    except ImportError:
        PYQT_AVAILABLE = False

if PYQT_AVAILABLE:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.config import validate_email, get_window_config, get_default_bcc, validate_bcc_email
    from utils.logger import get_logger


class EmailInputWidget(QWidget):
    """邮件输入组件"""

    # 信号定义
    email_send_requested = pyqtSignal(str, str, bool)  # 发送邮件请求信号 (email, bcc, bcc_enabled)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger()
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置UI布局"""
        layout = QVBoxLayout(self)
        
        # 标题标签
        title_label = QLabel("Betelligent邮件发送工具")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 输入区域
        input_layout = QHBoxLayout()
        
        # 邮箱标签
        email_label = QLabel("收件人邮箱:")
        email_label.setMinimumWidth(80)
        input_layout.addWidget(email_label)
        
        # 邮箱输入框
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("请输入收件人邮箱地址")
        self.email_input.setMinimumHeight(30)
        input_layout.addWidget(self.email_input)
        
        # 发送按钮
        self.send_button = QPushButton("发送")
        self.send_button.setMinimumWidth(80)
        self.send_button.setMinimumHeight(30)
        self.send_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        input_layout.addWidget(self.send_button)
        
        layout.addLayout(input_layout)

        # BCC配置区域
        bcc_group = QGroupBox("BCC配置")
        bcc_layout = QVBoxLayout(bcc_group)

        # BCC启用勾选框
        self.bcc_enabled_checkbox = QCheckBox("启用BCC（密送）")
        self.bcc_enabled_checkbox.setChecked(True)  # 默认启用
        bcc_layout.addWidget(self.bcc_enabled_checkbox)

        # BCC输入区域
        bcc_input_layout = QHBoxLayout()

        # BCC标签
        bcc_label = QLabel("BCC地址:")
        bcc_label.setMinimumWidth(80)
        bcc_input_layout.addWidget(bcc_label)

        # BCC输入框
        self.bcc_input = QLineEdit()
        self.bcc_input.setPlaceholderText("请输入BCC邮箱地址")
        self.bcc_input.setText(get_default_bcc())  # 设置默认值
        self.bcc_input.setMinimumHeight(25)
        bcc_input_layout.addWidget(self.bcc_input)

        bcc_layout.addLayout(bcc_input_layout)
        layout.addWidget(bcc_group)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)
    
    def setup_connections(self):
        """设置信号连接"""
        self.send_button.clicked.connect(self.on_send_clicked)
        self.email_input.returnPressed.connect(self.on_send_clicked)
        self.email_input.textChanged.connect(self.on_email_text_changed)
        self.bcc_enabled_checkbox.toggled.connect(self.on_bcc_enabled_changed)
        self.bcc_input.textChanged.connect(self.on_bcc_text_changed)
    
    def on_send_clicked(self):
        """发送按钮点击事件"""
        email = self.email_input.text().strip()

        if not email:
            self.show_message("请输入邮箱地址", "warning")
            return

        if not validate_email(email):
            self.show_message("请输入有效的邮箱地址", "warning")
            return

        # 获取BCC配置
        bcc_enabled = self.bcc_enabled_checkbox.isChecked()
        bcc_address = self.bcc_input.text().strip()

        # 如果启用BCC，验证BCC地址
        if bcc_enabled and bcc_address:
            if not validate_bcc_email(bcc_address):
                self.show_message("请输入有效的BCC邮箱地址", "warning")
                return

        # 发送信号
        self.email_send_requested.emit(email, bcc_address, bcc_enabled)
    
    def on_email_text_changed(self, text):
        """邮箱输入框文本变化事件"""
        # 实时验证邮箱格式
        is_valid = validate_email(text.strip()) if text.strip() else True
        
        # 设置输入框样式
        if text.strip() and not is_valid:
            self.email_input.setStyleSheet("QLineEdit { border: 2px solid red; }")
        else:
            self.email_input.setStyleSheet("")
        
        # 启用/禁用发送按钮
        self.send_button.setEnabled(bool(text.strip()))

    def on_bcc_enabled_changed(self, enabled):
        """BCC启用状态变化事件"""
        self.bcc_input.setEnabled(enabled)
        if enabled:
            self.bcc_input.setStyleSheet("")
        else:
            self.bcc_input.setStyleSheet("QLineEdit { background-color: #f0f0f0; }")

    def on_bcc_text_changed(self, text):
        """BCC输入框文本变化事件"""
        # 只有在启用BCC且有内容时才验证
        if self.bcc_enabled_checkbox.isChecked() and text.strip():
            is_valid = validate_bcc_email(text.strip())

            # 设置输入框样式
            if not is_valid:
                self.bcc_input.setStyleSheet("QLineEdit { border: 2px solid orange; }")
            else:
                self.bcc_input.setStyleSheet("")
        else:
            self.bcc_input.setStyleSheet("")
    
    def show_message(self, message: str, msg_type: str = "info"):
        """显示消息框"""
        if msg_type == "warning":
            QMessageBox.warning(self, "警告", message)
        elif msg_type == "error":
            QMessageBox.critical(self, "错误", message)
        else:
            QMessageBox.information(self, "信息", message)
    
    def clear_input(self):
        """清空输入框"""
        self.email_input.clear()
    
    def set_enabled(self, enabled: bool):
        """设置组件启用状态"""
        self.email_input.setEnabled(enabled)
        self.send_button.setEnabled(enabled and bool(self.email_input.text().strip()))
        self.bcc_enabled_checkbox.setEnabled(enabled)
        self.bcc_input.setEnabled(enabled and self.bcc_enabled_checkbox.isChecked())

    def get_bcc_config(self):
        """获取BCC配置"""
        return {
            "enabled": self.bcc_enabled_checkbox.isChecked(),
            "address": self.bcc_input.text().strip()
        }

    def set_bcc_config(self, enabled: bool, address: str = ""):
        """设置BCC配置"""
        self.bcc_enabled_checkbox.setChecked(enabled)
        if address:
            self.bcc_input.setText(address)


class HistoryTableWidget(QWidget):
    """历史记录表格组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger()
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI布局"""
        layout = QVBoxLayout(self)
        
        # 标题标签
        title_label = QLabel("发送历史记录")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 历史记录表格
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["邮箱地址", "发送时间", "状态"])
        
        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        # 设置列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 邮箱地址列自适应
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 时间列适应内容
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 状态列适应内容
        
        layout.addWidget(self.table)
    
    def add_record(self, email: str, timestamp: str, status: str):
        """添加历史记录"""
        row_count = self.table.rowCount()
        self.table.insertRow(0)  # 在顶部插入新行
        
        # 设置单元格内容
        self.table.setItem(0, 0, QTableWidgetItem(email))
        self.table.setItem(0, 1, QTableWidgetItem(timestamp))
        
        # 状态单元格带颜色
        status_item = QTableWidgetItem(status)
        if status == "成功":
            status_item.setBackground(QColor(200, 255, 200))  # 浅绿色
        elif status == "失败":
            status_item.setBackground(QColor(255, 200, 200))  # 浅红色
        else:
            status_item.setBackground(QColor(255, 255, 200))  # 浅黄色
        
        self.table.setItem(0, 2, status_item)
        
        # 限制最大记录数
        max_records = 15
        while self.table.rowCount() > max_records:
            self.table.removeRow(self.table.rowCount() - 1)
    
    def load_history(self, history_data: List[Dict[str, Any]]):
        """加载历史记录数据"""
        self.table.setRowCount(0)  # 清空表格
        
        for record in history_data:
            email = record.get("email", "")
            timestamp = record.get("timestamp", "")
            status = record.get("status", "未知")
            
            # 格式化时间戳
            try:
                if timestamp:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    formatted_time = "未知时间"
            except:
                formatted_time = timestamp
            
            # 格式化状态
            status_text = "成功" if status == "success" else "失败" if status == "error" else "警告"
            
            self.add_record(email, formatted_time, status_text)
    
    def clear_history(self):
        """清空历史记录"""
        self.table.setRowCount(0)


class StatusBarWidget(QStatusBar):
    """状态栏组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger()
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.showMessage("准备就绪")
        
        # 添加永久状态指示器
        self.permanent_label = QLabel("就绪")
        self.addPermanentWidget(self.permanent_label)
    
    def show_status(self, message: str, timeout: int = 0):
        """显示状态信息"""
        self.showMessage(message, timeout)
        self.logger.debug(f"Status: {message}")
    
    def show_progress(self, message: str):
        """显示进度信息"""
        self.show_status(f"正在处理: {message}")
        self.permanent_label.setText("忙碌")
    
    def show_ready(self):
        """显示就绪状态"""
        self.show_status("准备就绪")
        self.permanent_label.setText("就绪")
    
    def show_error(self, message: str):
        """显示错误信息"""
        self.show_status(f"错误: {message}", 5000)  # 5秒后自动清除
        self.permanent_label.setText("错误")
    
    def show_success(self, message: str):
        """显示成功信息"""
        self.show_status(f"成功: {message}", 3000)  # 3秒后自动清除
        self.permanent_label.setText("成功")
        
        # 3秒后恢复就绪状态
        QTimer.singleShot(3000, self.show_ready)
