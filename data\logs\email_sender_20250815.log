2025-08-15 13:56:32 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 13:56:32 - betelligent_email - INFO - Operation: get_dependencies | File: Notification for Betelligent account is activated.files | Status: success | Message: Found 7 dependency files
2025-08-15 13:56:43 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 13:57:49 - betelligent_email - INFO - 测试信息日志
2025-08-15 13:57:49 - betelligent_email - WARNING - 测试警告日志
2025-08-15 13:57:49 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 13:57:49 - betelligent_email - INFO - Operation: get_dependencies | File: Notification for Betelligent account is activated.files | Status: success | Message: Found 7 dependency files
2025-08-15 13:57:49 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 13:57:49 - betelligent_email - INFO - Operation: get_dependencies | File: Notification for Betelligent account is activated.files | Status: success | Message: Found 7 dependency files
2025-08-15 13:57:49 - betelligent_email - INFO - Operation: prepare_attachments | File: email_preparation | Status: success | Message: HTML content ready, 7 valid dependencies
2025-08-15 13:57:49 - betelligent_email - INFO - Outlook application created successfully
2025-08-15 13:59:09 - betelligent_email - INFO - No history file found, starting with empty history
2025-08-15 13:59:09 - betelligent_email - INFO - Main window initialized
2025-08-15 13:59:09 - betelligent_email - INFO - Application closing
2025-08-15 14:03:00 - betelligent_email - INFO - Application starting...
2025-08-15 14:03:00 - betelligent_email - INFO - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-08-15 14:03:00 - betelligent_email - INFO - Platform: win32
2025-08-15 14:03:02 - betelligent_email - INFO - Loaded 0 history records
2025-08-15 14:03:02 - betelligent_email - INFO - Main window initialized
2025-08-15 14:03:11 - betelligent_email - INFO - Application closing
2025-08-15 14:11:01 - betelligent_email - INFO - Loaded 0 history records
2025-08-15 14:11:01 - betelligent_email - INFO - Main window initialized
2025-08-15 14:11:01 - betelligent_email - INFO - Application closing
2025-08-15 14:13:59 - betelligent_email - INFO - Application starting...
2025-08-15 14:13:59 - betelligent_email - INFO - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-08-15 14:13:59 - betelligent_email - INFO - Platform: win32
2025-08-15 14:14:00 - betelligent_email - INFO - Loaded 0 history records
2025-08-15 14:14:00 - betelligent_email - INFO - Main window initialized
2025-08-15 14:14:17 - betelligent_email - INFO - Send email requested for: <EMAIL> (BCC: <EMAIL>)
2025-08-15 14:14:17 - betelligent_email - ERROR - Operation: send_email | Email: <EMAIL> | Status: error | Message: Outlook not available: (-**********, '尚未调用 CoInitialize。', None, None)
2025-08-15 14:14:59 - betelligent_email - INFO - Application closing
2025-08-15 14:16:48 - betelligent_email - INFO - Outlook application created successfully
2025-08-15 14:16:48 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 14:16:48 - betelligent_email - INFO - Operation: get_dependencies | File: Notification for Betelligent account is activated.files | Status: success | Message: Found 7 dependency files
2025-08-15 14:16:48 - betelligent_email - INFO - Operation: prepare_attachments | File: email_preparation | Status: success | Message: HTML content ready, 7 valid dependencies
2025-08-15 14:17:46 - betelligent_email - INFO - Outlook application created successfully
2025-08-15 14:17:46 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 14:17:46 - betelligent_email - INFO - Operation: get_dependencies | File: Notification for Betelligent account is activated.files | Status: success | Message: Found 7 dependency files
2025-08-15 14:17:46 - betelligent_email - INFO - Operation: prepare_attachments | File: email_preparation | Status: success | Message: HTML content ready, 7 valid dependencies
2025-08-15 14:20:38 - betelligent_email - INFO - 测试日志记录
2025-08-15 14:20:38 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 14:20:38 - betelligent_email - INFO - Operation: get_dependencies | File: Notification for Betelligent account is activated.files | Status: success | Message: Found 7 dependency files
2025-08-15 14:20:39 - betelligent_email - INFO - Loaded 1 history records
2025-08-15 14:20:39 - betelligent_email - INFO - Main window initialized
2025-08-15 14:20:39 - betelligent_email - INFO - Application closing
2025-08-15 14:21:01 - betelligent_email - INFO - Application starting...
2025-08-15 14:21:01 - betelligent_email - INFO - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-08-15 14:21:01 - betelligent_email - INFO - Platform: win32
2025-08-15 14:21:02 - betelligent_email - INFO - Loaded 1 history records
2025-08-15 14:21:02 - betelligent_email - INFO - Main window initialized
2025-08-15 14:21:20 - betelligent_email - INFO - Send email requested for: <EMAIL> (无BCC)
2025-08-15 14:21:20 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 14:21:20 - betelligent_email - INFO - Operation: get_dependencies | File: Notification for Betelligent account is activated.files | Status: success | Message: Found 7 dependency files
2025-08-15 14:21:20 - betelligent_email - INFO - Operation: prepare_attachments | File: email_preparation | Status: success | Message: HTML content ready, 7 valid dependencies
2025-08-15 14:21:20 - betelligent_email - INFO - Outlook application created successfully
2025-08-15 14:21:20 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\colorschememapping.xml: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:21:20 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\filelist.xml: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:21:20 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\image001.png: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:21:20 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\image002.gif: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:21:20 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\image003.png: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:21:20 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\image004.gif: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:21:20 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\themedata.thmx: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:21:20 - betelligent_email - INFO - Added 0 attachments out of 7
2025-08-15 14:21:20 - betelligent_email - INFO - Email sent successfully
2025-08-15 14:21:20 - betelligent_email - INFO - Operation: send_email | Email: <EMAIL> | Status: success | Message: Email sent successfully
2025-08-15 14:24:00 - betelligent_email - INFO - Send email requested for: <EMAIL> (BCC: <EMAIL>)
2025-08-15 14:24:00 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 14:24:00 - betelligent_email - INFO - Operation: get_dependencies | File: Notification for Betelligent account is activated.files | Status: success | Message: Found 7 dependency files
2025-08-15 14:24:00 - betelligent_email - INFO - Operation: prepare_attachments | File: email_preparation | Status: success | Message: HTML content ready, 7 valid dependencies
2025-08-15 14:24:00 - betelligent_email - INFO - Outlook application created successfully
2025-08-15 14:24:01 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\colorschememapping.xml: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:24:01 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\filelist.xml: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:24:01 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\image001.png: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:24:01 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\image002.gif: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:24:01 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\image003.png: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:24:01 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\image004.gif: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:24:01 - betelligent_email - ERROR - Failed to add attachment Notification for Betelligent account is activated.files\themedata.thmx: (-**********, '发生意外。', (4096, 'Microsoft Outlook', '路径不存在。请验证此路径是否正确。', None, 0, -**********), None)
2025-08-15 14:24:01 - betelligent_email - INFO - Added 0 attachments out of 7
2025-08-15 14:24:01 - betelligent_email - INFO - Email sent successfully
2025-08-15 14:24:01 - betelligent_email - INFO - Operation: send_email | Email: <EMAIL> | Status: success | Message: Email sent successfully
2025-08-15 14:25:07 - betelligent_email - INFO - Application closing
2025-08-15 14:25:20 - betelligent_email - INFO - Application starting...
2025-08-15 14:25:20 - betelligent_email - INFO - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-08-15 14:25:20 - betelligent_email - INFO - Platform: win32
2025-08-15 14:25:21 - betelligent_email - INFO - Loaded 3 history records
2025-08-15 14:25:21 - betelligent_email - INFO - Main window initialized
2025-08-15 14:26:29 - betelligent_email - INFO - Application closing
2025-08-15 14:33:42 - betelligent_email - INFO - Application starting...
2025-08-15 14:33:42 - betelligent_email - INFO - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-08-15 14:33:42 - betelligent_email - INFO - Platform: win32
2025-08-15 14:33:43 - betelligent_email - INFO - Loaded 3 history records
2025-08-15 14:33:43 - betelligent_email - INFO - Main window initialized
2025-08-15 14:44:21 - betelligent_email - INFO - Application closing
