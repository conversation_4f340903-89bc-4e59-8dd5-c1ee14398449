2025-08-15 13:56:32 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 13:56:32 - betelligent_email - INFO - Operation: get_dependencies | File: Notification for Betelligent account is activated.files | Status: success | Message: Found 7 dependency files
2025-08-15 13:56:43 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 13:57:49 - betelligent_email - INFO - 测试信息日志
2025-08-15 13:57:49 - betelligent_email - WARNING - 测试警告日志
2025-08-15 13:57:49 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 13:57:49 - betelligent_email - INFO - Operation: get_dependencies | File: Notification for Betelligent account is activated.files | Status: success | Message: Found 7 dependency files
2025-08-15 13:57:49 - betelligent_email - INFO - Operation: read_html | File: Notification for Betelligent account is activated.htm | Status: success | Message: Read with encoding: utf-16
2025-08-15 13:57:49 - betelligent_email - INFO - Operation: get_dependencies | File: Notification for Betelligent account is activated.files | Status: success | Message: Found 7 dependency files
2025-08-15 13:57:49 - betelligent_email - INFO - Operation: prepare_attachments | File: email_preparation | Status: success | Message: HTML content ready, 7 valid dependencies
2025-08-15 13:57:49 - betelligent_email - INFO - Outlook application created successfully
2025-08-15 13:59:09 - betelligent_email - INFO - No history file found, starting with empty history
2025-08-15 13:59:09 - betelligent_email - INFO - Main window initialized
2025-08-15 13:59:09 - betelligent_email - INFO - Application closing
2025-08-15 14:03:00 - betelligent_email - INFO - Application starting...
2025-08-15 14:03:00 - betelligent_email - INFO - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-08-15 14:03:00 - betelligent_email - INFO - Platform: win32
2025-08-15 14:03:02 - betelligent_email - INFO - Loaded 0 history records
2025-08-15 14:03:02 - betelligent_email - INFO - Main window initialized
2025-08-15 14:03:11 - betelligent_email - INFO - Application closing
