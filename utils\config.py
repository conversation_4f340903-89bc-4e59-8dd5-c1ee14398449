"""
配置管理模块

管理应用程序的配置信息，包括邮件设置、文件路径等。
提供配置验证和默认值管理功能。
"""

import re
import os
import json
from typing import Dict, Any, Optional
from .logger import get_logger


# 邮件配置常量
EMAIL_CONFIG = {
    "subject": "Notification for Betelligent account is activated",
    "bcc": "Kell yan <<EMAIL>>",
    "html_file": "Notification for Betelligent account is activated.htm",
    "dependencies_dir": "Notification for Betelligent account is activated.files"
}

# 应用程序配置
APP_CONFIG = {
    "window_title": "Betelligent邮件发送工具",
    "window_width": 800,
    "window_height": 600,
    "max_history_records": 15,
    "log_level": "INFO",
    "auto_cleanup_logs": True,
    "log_retention_days": 30
}

# 文件路径配置
PATH_CONFIG = {
    "data_dir": "data",
    "logs_dir": "data/logs", 
    "history_file": "data/send_history.json",
    "config_file": "config.json",
    "templates_dir": "templates"
}


def get_config(config_type: str = "all") -> Dict[str, Any]:
    """
    获取配置信息
    
    Args:
        config_type: 配置类型 ("email", "app", "path", "all")
        
    Returns:
        配置字典
    """
    config_map = {
        "email": EMAIL_CONFIG,
        "app": APP_CONFIG, 
        "path": PATH_CONFIG
    }
    
    if config_type == "all":
        return {
            "email": EMAIL_CONFIG,
            "app": APP_CONFIG,
            "path": PATH_CONFIG
        }
    
    return config_map.get(config_type, {})


def load_config_file(config_file: str = "config.json") -> Dict[str, Any]:
    """
    从文件加载配置
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        配置字典
    """
    logger = get_logger()
    
    if not os.path.exists(config_file):
        logger.warning(f"Config file not found: {config_file}, using defaults")
        return {}
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info(f"Loaded config from: {config_file}")
        return config
    except Exception as e:
        logger.error(f"Error loading config file {config_file}: {str(e)}")
        return {}


def save_config_file(config: Dict[str, Any], config_file: str = "config.json") -> bool:
    """
    保存配置到文件
    
    Args:
        config: 配置字典
        config_file: 配置文件路径
        
    Returns:
        是否保存成功
    """
    logger = get_logger()
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(config_file) if os.path.dirname(config_file) else ".", exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Config saved to: {config_file}")
        return True
    except Exception as e:
        logger.error(f"Error saving config file {config_file}: {str(e)}")
        return False


def validate_email(email: str) -> bool:
    """
    验证邮箱地址格式
    
    Args:
        email: 邮箱地址
        
    Returns:
        是否为有效邮箱格式
    """
    if not email or not isinstance(email, str):
        return False
    
    # 邮箱格式正则表达式
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(email_pattern, email.strip()))


def validate_file_path(file_path: str) -> bool:
    """
    验证文件路径是否存在
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件是否存在
    """
    return os.path.exists(file_path) and os.path.isfile(file_path)


def validate_directory_path(dir_path: str) -> bool:
    """
    验证目录路径是否存在
    
    Args:
        dir_path: 目录路径
        
    Returns:
        目录是否存在
    """
    return os.path.exists(dir_path) and os.path.isdir(dir_path)


def get_html_file_path() -> str:
    """
    获取HTML文件的完整路径
    
    Returns:
        HTML文件路径
    """
    return EMAIL_CONFIG["html_file"]


def get_dependencies_dir_path() -> str:
    """
    获取依赖文件目录的完整路径
    
    Returns:
        依赖文件目录路径
    """
    return EMAIL_CONFIG["dependencies_dir"]


def create_required_directories() -> None:
    """
    创建必需的目录结构
    """
    logger = get_logger()
    
    directories = [
        PATH_CONFIG["data_dir"],
        PATH_CONFIG["logs_dir"],
        PATH_CONFIG["templates_dir"]
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            logger.debug(f"Created/verified directory: {directory}")
        except Exception as e:
            logger.error(f"Error creating directory {directory}: {str(e)}")


def get_email_subject() -> str:
    """获取邮件主题"""
    return EMAIL_CONFIG["subject"]


def get_email_bcc() -> str:
    """获取邮件BCC地址"""
    return EMAIL_CONFIG["bcc"]


def get_max_history_records() -> int:
    """获取最大历史记录数"""
    return APP_CONFIG["max_history_records"]


def get_window_config() -> Dict[str, Any]:
    """
    获取窗口配置
    
    Returns:
        窗口配置字典
    """
    return {
        "title": APP_CONFIG["window_title"],
        "width": APP_CONFIG["window_width"],
        "height": APP_CONFIG["window_height"]
    }


def merge_config(default_config: Dict[str, Any], user_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    合并默认配置和用户配置
    
    Args:
        default_config: 默认配置
        user_config: 用户配置
        
    Returns:
        合并后的配置
    """
    merged = default_config.copy()
    
    for key, value in user_config.items():
        if isinstance(value, dict) and key in merged and isinstance(merged[key], dict):
            merged[key] = merge_config(merged[key], value)
        else:
            merged[key] = value
    
    return merged
