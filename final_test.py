# -*- coding: utf-8 -*-
"""
最终功能测试脚本 - 验证所有功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_all_functionality():
    """测试所有功能"""
    print("🚀 Betelligent邮件发送工具 - 最终功能测试")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 0
    
    # 测试1: 基本模块导入
    total_tests += 1
    print(f"\n📦 测试 {total_tests}: 基本模块导入")
    try:
        from utils.logger import setup_logger, get_logger
        from utils.config import validate_email, validate_bcc_email, get_default_bcc
        from utils.file_handler import read_html_file, get_dependency_files
        from utils.email_sender import check_outlook_availability
        print("✅ 所有核心模块导入成功")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
    
    # 测试2: 日志系统
    total_tests += 1
    print(f"\n📝 测试 {total_tests}: 日志系统")
    try:
        logger = setup_logger()
        logger.info("测试日志记录")
        print("✅ 日志系统正常工作")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 日志系统失败: {e}")
    
    # 测试3: 邮箱验证
    total_tests += 1
    print(f"\n📧 测试 {total_tests}: 邮箱验证功能")
    try:
        # 测试普通邮箱验证
        assert validate_email("<EMAIL>") == True
        assert validate_email("invalid-email") == False
        
        # 测试BCC邮箱验证
        assert validate_bcc_email("Kell yan <<EMAIL>>") == True
        assert validate_bcc_email("invalid") == False
        
        print("✅ 邮箱验证功能正常")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 邮箱验证失败: {e}")
    
    # 测试4: 文件处理
    total_tests += 1
    print(f"\n📄 测试 {total_tests}: 文件处理功能")
    try:
        html_content = read_html_file()
        dependencies = get_dependency_files()
        
        if html_content and len(dependencies) > 0:
            print(f"✅ 文件处理正常 (HTML: {len(html_content)} 字符, 依赖: {len(dependencies)} 个)")
            tests_passed += 1
        else:
            print("❌ 文件处理失败: HTML内容或依赖文件为空")
    except Exception as e:
        print(f"❌ 文件处理失败: {e}")
    
    # 测试5: Outlook集成
    total_tests += 1
    print(f"\n🔗 测试 {total_tests}: Outlook集成")
    try:
        available, message = check_outlook_availability()
        if available:
            print(f"✅ Outlook集成正常: {message}")
            tests_passed += 1
        else:
            print(f"⚠️  Outlook不可用: {message}")
            print("   这可能是正常的，如果Outlook未运行或未安装")
    except Exception as e:
        print(f"❌ Outlook集成测试失败: {e}")
    
    # 测试6: GUI组件
    total_tests += 1
    print(f"\n🖥️  测试 {total_tests}: GUI组件")
    try:
        # 检查PyQt可用性
        try:
            from PyQt5.QtWidgets import QApplication
            pyqt_version = "PyQt5"
        except ImportError:
            from PyQt6.QtWidgets import QApplication
            pyqt_version = "PyQt6"
        
        # 创建应用程序（不显示）
        app = QApplication(sys.argv)
        
        # 导入GUI组件
        from gui.components import EmailInputWidget, HistoryTableWidget
        from gui.main_window import MainWindow
        
        # 创建主窗口（不显示）
        window = MainWindow()
        
        # 测试BCC配置
        bcc_config = window.email_input_widget.get_bcc_config()
        expected_bcc = get_default_bcc()
        
        if bcc_config["enabled"] and bcc_config["address"] == expected_bcc:
            print(f"✅ GUI组件正常 (使用 {pyqt_version})")
            tests_passed += 1
        else:
            print(f"❌ GUI BCC配置异常: {bcc_config}")
        
        # 清理
        window.close()
        app.quit()
        
    except Exception as e:
        print(f"❌ GUI组件测试失败: {e}")
    
    # 测试7: 配置管理
    total_tests += 1
    print(f"\n⚙️  测试 {total_tests}: 配置管理")
    try:
        from utils.config import get_config, get_email_subject, get_email_bcc
        
        config = get_config("email")
        subject = get_email_subject()
        bcc = get_email_bcc()
        
        expected_subject = "Notification for Betelligent account is activated"
        expected_bcc = "Kell yan <<EMAIL>>"
        
        if subject == expected_subject and bcc == expected_bcc:
            print("✅ 配置管理正常")
            tests_passed += 1
        else:
            print(f"❌ 配置不匹配: subject={subject}, bcc={bcc}")
    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
    
    # 测试结果汇总
    print("\n" + "=" * 60)
    print(f"📊 测试结果汇总: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！程序可以正常使用。")
        success_rate = 100
    else:
        success_rate = (tests_passed / total_tests) * 100
        print(f"⚠️  成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("✅ 大部分功能正常，程序基本可用。")
        elif success_rate >= 60:
            print("⚠️  部分功能异常，建议检查相关配置。")
        else:
            print("❌ 多个功能异常，请检查安装和配置。")
    
    # 使用建议
    print("\n📋 使用建议:")
    if tests_passed >= 6:
        print("• 可以正常使用主程序发送邮件")
        print("• 建议先用测试邮箱验证功能")
    
    if tests_passed < total_tests:
        print("• 查看日志文件获取详细错误信息")
        print("• 参考故障排除指南解决问题")
        print("• 确保Outlook已安装并正在运行")
    
    print(f"\n🚀 启动主程序: python main.py")
    print(f"📚 查看文档: docs/用户使用手册.md")
    print(f"🔧 故障排除: docs/故障排除指南.md")
    
    return tests_passed == total_tests


if __name__ == "__main__":
    success = test_all_functionality()
    sys.exit(0 if success else 1)
