# Betelligent邮件发送工具 - 故障排除指南

## 常见错误及解决方案

### 1. COM初始化错误

**错误信息**: "尚未调用 CoInitialize" 或 "(-2147221008, '尚 未调用 CoInitialize。', None, None)"

**原因**: Windows COM组件未正确初始化

**解决方案**:
1. 确保Microsoft Outlook已安装并正在运行
2. 重启程序
3. 以管理员身份运行程序
4. 重启Outlook后再试

### 2. Outlook不可用错误

**错误信息**: "Outlook not available"

**可能原因及解决方案**:

#### 2.1 Outlook未安装
- 确保已安装Microsoft Outlook（不是Outlook Web App）
- 支持的版本：Outlook 2016, 2019, 2021, Microsoft 365

#### 2.2 Outlook未运行
- 启动Microsoft Outlook
- 确保Outlook完全加载完成
- 检查Outlook是否有错误对话框需要处理

#### 2.3 Outlook配置问题
- 确保Outlook已配置至少一个邮箱账户
- 检查邮箱账户是否正常工作
- 尝试手动发送一封测试邮件

### 3. 权限相关错误

**错误信息**: 权限被拒绝或访问被拒绝

**解决方案**:
1. 以管理员身份运行程序
2. 检查Windows防火墙设置
3. 检查杀毒软件是否阻止了程序
4. 确保用户账户有足够的权限

### 4. 邮件发送失败

**错误信息**: "Failed to send email"

**可能原因及解决方案**:

#### 4.1 网络连接问题
- 检查网络连接
- 确保可以访问邮件服务器
- 检查代理设置

#### 4.2 邮箱配置问题
- 确认Outlook邮箱配置正确
- 检查发送邮件设置
- 验证SMTP服务器设置

#### 4.3 邮件内容问题
- 检查收件人邮箱地址格式
- 验证BCC地址格式（如果启用）
- 确保HTML模板文件存在

### 5. HTML文件相关错误

**错误信息**: "HTML文件未找到" 或 "Failed to read HTML file"

**解决方案**:
1. 确保以下文件存在于程序目录：
   - `Notification for Betelligent account is activated.htm`
   - `Notification for Betelligent account is activated.files/` 目录

2. 检查文件权限：
   - 确保程序有读取文件的权限
   - 检查文件是否被其他程序占用

3. 文件编码问题：
   - 程序会自动尝试多种编码格式
   - 如果仍有问题，请检查HTML文件编码

### 6. GUI界面问题

**错误信息**: PyQt相关错误或界面显示异常

**解决方案**:

#### 6.1 PyQt未安装
```bash
pip install PyQt5
# 或
pip install PyQt6
```

#### 6.2 界面显示问题
- 调整Windows显示缩放设置
- 更新显卡驱动
- 重启程序

#### 6.3 字体显示问题
- 确保系统安装了中文字体
- 检查Windows区域和语言设置

### 7. 依赖库问题

**错误信息**: 模块导入失败

**解决方案**:
1. 安装所需依赖：
```bash
pip install PyQt5 pywin32
```

2. 检查Python版本：
   - 需要Python 3.7或更高版本
   - 推荐使用Python 3.8+

3. 虚拟环境问题：
   - 确保在正确的Python环境中运行
   - 检查环境变量设置

## 调试步骤

### 1. 基本检查
1. 确认系统要求：
   - Windows操作系统
   - Python 3.7+
   - Microsoft Outlook已安装

2. 检查依赖：
```bash
python -c "import PyQt5; print('PyQt5 OK')"
python -c "import win32com.client; print('win32com OK')"
```

3. 检查Outlook：
```bash
python -c "from utils.email_sender import check_outlook_availability; print(check_outlook_availability())"
```

### 2. 日志分析
查看日志文件获取详细错误信息：
- 位置：`data/logs/email_sender_YYYYMMDD.log`
- 查找ERROR和WARNING级别的消息
- 注意时间戳和错误详情

### 3. 测试脚本
运行测试脚本验证功能：
```bash
python test_email_send.py
python test_bcc_gui.py
```

### 4. 逐步测试
1. 测试模块导入
2. 测试Outlook连接
3. 测试HTML文件读取
4. 测试GUI组件创建
5. 测试邮件发送（不实际发送）

## 性能优化

### 1. 启动速度优化
- 确保Outlook已经运行
- 关闭不必要的后台程序
- 使用SSD硬盘

### 2. 发送速度优化
- 确保网络连接稳定
- 减少HTML文件大小（如果可能）
- 优化附件数量

### 3. 内存使用优化
- 定期重启程序
- 清理临时文件
- 关闭其他占用内存的程序

## 联系技术支持

如果以上解决方案都无法解决问题，请收集以下信息联系技术支持：

### 必需信息
1. **错误信息**：完整的错误消息和堆栈跟踪
2. **系统信息**：
   - Windows版本
   - Python版本
   - Outlook版本
3. **日志文件**：最新的日志文件内容
4. **操作步骤**：重现问题的详细步骤

### 可选信息
1. 屏幕截图
2. 系统配置信息
3. 网络环境描述
4. 其他相关软件信息

### 提交方式
1. 发送邮件到技术支持邮箱
2. 在内部系统中创建支持票据
3. 联系开发团队

## 预防措施

### 1. 定期维护
- 定期清理日志文件
- 更新依赖库
- 备份配置文件

### 2. 环境检查
- 定期检查Outlook状态
- 验证网络连接
- 监控系统资源使用

### 3. 最佳实践
- 使用有效的邮箱地址
- 定期测试发送功能
- 保持程序和依赖库更新

---

**注意**: 本指南涵盖了大部分常见问题。如果遇到未列出的问题，请参考日志文件或联系技术支持。
