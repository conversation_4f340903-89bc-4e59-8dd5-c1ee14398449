# -*- coding: utf-8 -*-
"""
邮件发送功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_email_send_dry_run():
    """测试邮件发送功能（不实际发送）"""
    print("=" * 50)
    print("测试邮件发送功能...")
    
    try:
        from utils.email_sender import (
            check_outlook_availability, 
            create_outlook_application,
            create_email_item,
            set_email_properties,
            set_email_html_content
        )
        from utils.file_handler import prepare_email_attachments
        
        # 检查Outlook可用性
        available, message = check_outlook_availability()
        if not available:
            print(f"✗ Outlook不可用: {message}")
            return False
        print("✓ Outlook可用性检查通过")
        
        # 创建Outlook应用程序
        outlook_app = create_outlook_application()
        if not outlook_app:
            print("✗ Outlook应用程序创建失败")
            return False
        print("✓ Outlook应用程序创建成功")
        
        # 创建邮件项目
        mail_item = create_email_item(outlook_app)
        if not mail_item:
            print("✗ 邮件项目创建失败")
            return False
        print("✓ 邮件项目创建成功")
        
        # 设置邮件属性（测试邮箱）
        test_email = "<EMAIL>"
        test_bcc = "Kell yan <<EMAIL>>"
        
        if not set_email_properties(mail_item, test_email, bcc_address=test_bcc):
            print("✗ 邮件属性设置失败")
            return False
        print("✓ 邮件属性设置成功")
        
        # 准备邮件内容
        html_content, attachments = prepare_email_attachments()
        if not html_content:
            print("✗ HTML内容准备失败")
            return False
        print(f"✓ HTML内容准备成功 (长度: {len(html_content)})")
        
        # 设置HTML内容
        if not set_email_html_content(mail_item, html_content):
            print("✗ HTML内容设置失败")
            return False
        print("✓ HTML内容设置成功")
        
        print(f"✓ 找到 {len(attachments)} 个附件文件")
        
        # 不实际发送，只是验证邮件对象创建成功
        print("✓ 邮件发送功能测试完成（未实际发送）")
        
        # 清理COM
        import pythoncom
        try:
            pythoncom.CoUninitialize()
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"✗ 邮件发送功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("Betelligent邮件发送工具 - 邮件发送功能测试")
    print("=" * 50)
    
    success = test_email_send_dry_run()
    
    if success:
        print("\n🎉 邮件发送功能测试通过！")
        print("注意：这只是功能测试，没有实际发送邮件。")
        print("要测试实际发送，请使用主程序并输入真实的邮箱地址。")
        return 0
    else:
        print("\n⚠️  邮件发送功能测试失败。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
