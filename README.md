# Betelligent邮件发送工具

一个基于PyQt的GUI应用程序，用于通过Microsoft Outlook发送HTML格式的Betelligent账户激活通知邮件。

## 功能特性

- 🎯 **专用邮件发送**: 专门用于发送Betelligent账户激活通知
- 📧 **HTML邮件支持**: 发送包含图片和样式的HTML格式邮件
- 🖥️ **用户友好界面**: 基于PyQt的现代化图形界面
- 📊 **发送历史**: 显示最近15次邮件发送记录
- 📝 **详细日志**: 完整的操作日志记录
- ⚡ **Outlook集成**: 通过COM接口与Microsoft Outlook无缝集成
- 🔧 **BCC配置**: 可配置的BCC（密送）功能，支持启用/禁用
- 🔒 **安全设置**: 预设邮件主题和默认BCC地址

## 系统要求

- **操作系统**: Windows 7/8/10/11
- **Python**: 3.7或更高版本
- **Microsoft Outlook**: 已安装并配置
- **依赖库**: PyQt5/PyQt6, pywin32

## 安装说明

### 1. 克隆或下载项目
```bash
git clone <repository-url>
cd sendemail
```

### 2. 安装Python依赖
```bash
pip install -r requirements.txt
```

或手动安装：
```bash
pip install PyQt5 pywin32
```

### 3. 确保Outlook正常运行
- 启动Microsoft Outlook
- 确保已配置邮箱账户
- 确保Outlook可以正常发送邮件

## 使用方法

### 启动程序
```bash
python main.py
```

### 发送邮件
1. 在"收件人邮箱"输入框中输入目标邮箱地址
2. 配置BCC设置（可选）：
   - 勾选或取消"启用BCC（密送）"
   - 修改BCC地址（默认：Kell yan <<EMAIL>>）
3. 点击"发送"按钮或按回车键
4. 程序会自动：
   - 验证邮箱地址格式
   - 验证BCC地址格式（如果启用）
   - 读取HTML邮件模板
   - 通过Outlook发送邮件（包含BCC）
   - 记录发送结果

### 查看历史
- 发送历史会自动显示在界面下方的表格中
- 显示最近15次发送记录
- 包含邮箱地址、发送时间和状态信息

## 项目结构

```
sendemail/
├── main.py                                    # 主程序入口
├── gui/                                       # GUI界面模块
│   ├── __init__.py
│   ├── main_window.py                         # 主窗口
│   └── components.py                          # UI组件
├── utils/                                     # 工具函数模块
│   ├── __init__.py
│   ├── email_sender.py                        # 邮件发送功能
│   ├── file_handler.py                        # 文件处理功能
│   ├── logger.py                              # 日志系统
│   └── config.py                              # 配置管理
├── data/                                      # 数据目录
│   ├── send_history.json                      # 发送历史记录
│   └── logs/                                  # 日志文件
├── docs/                                      # 文档目录
│   ├── 技术设计文档.md
│   └── 开发计划.md
├── Notification for Betelligent account is activated.htm    # HTML邮件模板
├── Notification for Betelligent account is activated.files/ # 邮件依赖文件
├── requirements.txt                           # 依赖包列表
└── README.md                                  # 项目说明
```

## 配置说明

### 邮件配置
程序使用以下配置：
- **邮件主题**: "Notification for Betelligent account is activated"（固定）
- **BCC地址**: "Kell yan <<EMAIL>>"（可配置，可禁用）
- **邮件格式**: HTML格式
- **模板文件**: "Notification for Betelligent account is activated.htm"

### 日志配置
- 日志文件位置: `data/logs/`
- 日志文件命名: `email_sender_YYYYMMDD.log`
- 日志级别: INFO（可在代码中调整）
- 自动清理: 保留30天的日志文件

## 故障排除

### 常见问题

1. **"win32com.client module not available"**
   - 解决方案: 安装pywin32库
   ```bash
   pip install pywin32
   ```

2. **"Outlook not available"**
   - 确保Microsoft Outlook已安装并正在运行
   - 检查Outlook是否已配置邮箱账户
   - 尝试重启Outlook

3. **"PyQt5/PyQt6 not found"**
   - 解决方案: 安装PyQt库
   ```bash
   pip install PyQt5
   # 或
   pip install PyQt6
   ```

4. **"HTML文件未找到"**
   - 确保HTML模板文件存在于项目根目录
   - 检查文件名是否正确
   - 确保依赖文件目录存在

5. **邮件发送失败**
   - 检查网络连接
   - 确认Outlook账户配置正确
   - 查看日志文件获取详细错误信息

### 日志查看
查看详细的错误信息和操作记录：
```
data/logs/email_sender_YYYYMMDD.log
```

### 详细故障排除
如需更详细的故障排除指南，请参考：
- `docs/故障排除指南.md` - 完整的问题解决方案
- `docs/用户使用手册.md` - 详细的使用说明

## 开发说明

### 代码结构
- 采用函数式编程设计
- 模块化架构，便于维护和扩展
- 完整的错误处理和日志记录

### 扩展功能
如需添加新功能，可以：
1. 在`utils/`目录添加新的工具模块
2. 在`gui/`目录添加新的界面组件
3. 更新`main.py`中的导入和初始化代码

### 测试
建议在发送真实邮件前：
1. 使用测试邮箱地址
2. 检查Outlook的发件箱
3. 确认邮件内容和格式正确

## 许可证

本项目仅供内部使用，请勿外传。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 本工具专门为Betelligent系统设计，请确保在正确的环境中使用。
