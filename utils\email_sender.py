"""
邮件发送模块

通过Outlook COM接口发送HTML邮件的核心功能。
支持HTML内容、附件和BCC设置。
"""

import os
from typing import List, Dict, Optional, Tuple
from .logger import get_logger, log_email_operation
from .config import get_email_subject, get_email_bcc, validate_email
from .file_handler import prepare_email_attachments

try:
    import win32com.client
    COM_AVAILABLE = True
except ImportError:
    COM_AVAILABLE = False


def check_outlook_availability() -> Tuple[bool, str]:
    """
    检查Outlook是否可用
    
    Returns:
        (是否可用, 状态信息)
    """
    if not COM_AVAILABLE:
        return False, "win32com.client module not available"
    
    try:
        outlook = win32com.client.Dispatch("Outlook.Application")
        # 尝试访问命名空间来验证Outlook是否正常运行
        namespace = outlook.GetNamespace("MAPI")
        return True, "Outlook is available"
    except Exception as e:
        return False, f"Outlook not available: {str(e)}"


def create_outlook_application() -> Optional[object]:
    """
    创建Outlook应用程序对象
    
    Returns:
        Outlook应用程序对象，失败返回None
    """
    logger = get_logger()
    
    if not COM_AVAILABLE:
        logger.error("win32com.client module not available")
        return None
    
    try:
        outlook = win32com.client.Dispatch("Outlook.Application")
        logger.info("Outlook application created successfully")
        return outlook
    except Exception as e:
        logger.error(f"Failed to create Outlook application: {str(e)}")
        return None


def create_email_item(outlook_app: object) -> Optional[object]:
    """
    创建邮件项目
    
    Args:
        outlook_app: Outlook应用程序对象
        
    Returns:
        邮件项目对象，失败返回None
    """
    logger = get_logger()
    
    try:
        # 创建邮件项目 (olMailItem = 0)
        mail_item = outlook_app.CreateItem(0)
        logger.debug("Email item created successfully")
        return mail_item
    except Exception as e:
        logger.error(f"Failed to create email item: {str(e)}")
        return None


def set_email_properties(
    mail_item: object,
    to_address: str,
    subject: Optional[str] = None,
    bcc_address: Optional[str] = None
) -> bool:
    """
    设置邮件基本属性
    
    Args:
        mail_item: 邮件项目对象
        to_address: 收件人地址
        subject: 邮件主题
        bcc_address: BCC地址
        
    Returns:
        是否设置成功
    """
    logger = get_logger()
    
    try:
        # 设置收件人
        mail_item.To = to_address
        
        # 设置主题
        if subject is None:
            subject = get_email_subject()
        mail_item.Subject = subject
        
        # 设置BCC
        if bcc_address is None:
            bcc_address = get_email_bcc()
        if bcc_address:
            mail_item.BCC = bcc_address
        
        logger.debug(f"Email properties set - To: {to_address}, Subject: {subject}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to set email properties: {str(e)}")
        return False


def set_email_html_content(mail_item: object, html_content: str) -> bool:
    """
    设置邮件HTML内容
    
    Args:
        mail_item: 邮件项目对象
        html_content: HTML内容
        
    Returns:
        是否设置成功
    """
    logger = get_logger()
    
    try:
        mail_item.HTMLBody = html_content
        logger.debug("HTML content set successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to set HTML content: {str(e)}")
        return False


def add_email_attachments(mail_item: object, attachment_files: List[str]) -> int:
    """
    添加邮件附件
    
    Args:
        mail_item: 邮件项目对象
        attachment_files: 附件文件路径列表
        
    Returns:
        成功添加的附件数量
    """
    logger = get_logger()
    added_count = 0
    
    for file_path in attachment_files:
        try:
            if os.path.exists(file_path):
                mail_item.Attachments.Add(file_path)
                added_count += 1
                logger.debug(f"Added attachment: {file_path}")
            else:
                logger.warning(f"Attachment file not found: {file_path}")
        except Exception as e:
            logger.error(f"Failed to add attachment {file_path}: {str(e)}")
    
    logger.info(f"Added {added_count} attachments out of {len(attachment_files)}")
    return added_count


def send_email_item(mail_item: object) -> bool:
    """
    发送邮件
    
    Args:
        mail_item: 邮件项目对象
        
    Returns:
        是否发送成功
    """
    logger = get_logger()
    
    try:
        mail_item.Send()
        logger.info("Email sent successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to send email: {str(e)}")
        return False


def create_email_content() -> Tuple[str, List[str]]:
    """
    创建邮件内容和附件列表
    
    Returns:
        (HTML内容, 附件文件列表)
    """
    return prepare_email_attachments()


def send_email_via_outlook(
    to_address: str,
    subject: Optional[str] = None,
    bcc_address: Optional[str] = None,
    html_content: Optional[str] = None,
    attachment_files: Optional[List[str]] = None
) -> Dict[str, any]:
    """
    通过Outlook发送邮件的主函数
    
    Args:
        to_address: 收件人邮箱地址
        subject: 邮件主题（可选，使用默认值）
        bcc_address: BCC地址（可选，使用默认值）
        html_content: HTML内容（可选，自动读取）
        attachment_files: 附件文件列表（可选，自动获取）
        
    Returns:
        发送结果字典 {"success": bool, "message": str, "details": dict}
    """
    logger = get_logger()
    result = {
        "success": False,
        "message": "",
        "details": {
            "to_address": to_address,
            "attachments_count": 0,
            "html_content_length": 0
        }
    }
    
    # 验证邮箱地址
    if not validate_email(to_address):
        result["message"] = "Invalid email address format"
        log_email_operation("send_email", to_address, "error", result["message"])
        return result
    
    # 检查Outlook可用性
    outlook_available, outlook_message = check_outlook_availability()
    if not outlook_available:
        result["message"] = outlook_message
        log_email_operation("send_email", to_address, "error", result["message"])
        return result
    
    # 准备邮件内容和附件
    if html_content is None or attachment_files is None:
        html_content, attachment_files = create_email_content()
    
    if not html_content:
        result["message"] = "Failed to read HTML content"
        log_email_operation("send_email", to_address, "error", result["message"])
        return result
    
    result["details"]["html_content_length"] = len(html_content)
    result["details"]["attachments_count"] = len(attachment_files)
    
    try:
        # 创建Outlook应用程序
        outlook_app = create_outlook_application()
        if outlook_app is None:
            result["message"] = "Failed to create Outlook application"
            log_email_operation("send_email", to_address, "error", result["message"])
            return result
        
        # 创建邮件项目
        mail_item = create_email_item(outlook_app)
        if mail_item is None:
            result["message"] = "Failed to create email item"
            log_email_operation("send_email", to_address, "error", result["message"])
            return result
        
        # 设置邮件属性
        if not set_email_properties(mail_item, to_address, subject, bcc_address):
            result["message"] = "Failed to set email properties"
            log_email_operation("send_email", to_address, "error", result["message"])
            return result
        
        # 设置HTML内容
        if not set_email_html_content(mail_item, html_content):
            result["message"] = "Failed to set HTML content"
            log_email_operation("send_email", to_address, "error", result["message"])
            return result
        
        # 添加附件
        added_attachments = add_email_attachments(mail_item, attachment_files)
        result["details"]["attachments_added"] = added_attachments
        
        # 发送邮件
        if send_email_item(mail_item):
            result["success"] = True
            result["message"] = "Email sent successfully"
            log_email_operation("send_email", to_address, "success", result["message"])
        else:
            result["message"] = "Failed to send email"
            log_email_operation("send_email", to_address, "error", result["message"])
    
    except Exception as e:
        result["message"] = f"Unexpected error: {str(e)}"
        log_email_operation("send_email", to_address, "error", result["message"])
    
    return result
