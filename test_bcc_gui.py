# -*- coding: utf-8 -*-
"""
简单的BCC GUI测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_bcc_gui():
    """测试BCC GUI功能"""
    try:
        # 导入PyQt
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 导入并创建主窗口
        from gui.main_window import MainWindow
        
        window = MainWindow()
        print("主窗口创建成功")
        
        # 测试BCC配置
        bcc_config = window.email_input_widget.get_bcc_config()
        print(f"默认BCC配置: {bcc_config}")
        
        # 检查BCC组件是否存在
        if hasattr(window.email_input_widget, 'bcc_enabled_checkbox'):
            print("✓ BCC启用勾选框存在")
        else:
            print("✗ BCC启用勾选框不存在")
            return False
            
        if hasattr(window.email_input_widget, 'bcc_input'):
            print("✓ BCC输入框存在")
        else:
            print("✗ BCC输入框不存在")
            return False
        
        # 清理
        window.close()
        app.quit()
        
        print("BCC GUI测试完成 - 所有组件正常")
        return True
        
    except Exception as e:
        print(f"BCC GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_bcc_gui()
    sys.exit(0 if success else 1)
