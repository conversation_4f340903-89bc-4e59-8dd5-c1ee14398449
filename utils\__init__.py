"""
Betelligent邮件发送工具 - 工具函数模块

该模块包含邮件发送工具的核心功能函数，采用函数式编程设计。

模块说明:
- logger.py: 日志系统
- config.py: 配置管理
- file_handler.py: 文件处理功能
- email_sender.py: 邮件发送功能
"""

__version__ = "1.0.0"
__author__ = "Betelligent Team"

# 导入主要功能函数
from .logger import setup_logger, get_logger
from .config import get_config, validate_email
from .file_handler import read_html_file, get_dependency_files, validate_file_exists
from .email_sender import send_email_via_outlook, create_email_content

__all__ = [
    'setup_logger',
    'get_logger', 
    'get_config',
    'validate_email',
    'read_html_file',
    'get_dependency_files',
    'validate_file_exists',
    'send_email_via_outlook',
    'create_email_content'
]
