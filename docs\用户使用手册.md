# Betelligent邮件发送工具 - 用户使用手册

## 概述

Betelligent邮件发送工具是一个专门用于发送Betelligent账户激活通知邮件的桌面应用程序。该工具通过Microsoft Outlook发送包含HTML格式的通知邮件，并提供发送历史记录功能。

## 系统要求

### 必需环境
- **操作系统**: Windows 7/8/10/11
- **Microsoft Outlook**: 已安装并配置邮箱账户
- **Python**: 3.7或更高版本（如果从源码运行）

### 依赖软件
- PyQt5或PyQt6（图形界面库）
- pywin32（Windows COM接口）

## 安装指南

### 方法1: 直接运行（推荐）
如果您收到的是可执行文件：
1. 双击 `Betelligent邮件发送工具.exe` 启动程序
2. 确保Microsoft Outlook已安装并正在运行

### 方法2: 从源码运行
如果您收到的是源码包：

1. **安装Python依赖**
   ```bash
   pip install PyQt5 pywin32
   ```

2. **运行程序**
   ```bash
   python main.py
   ```

## 使用说明

### 启动程序

1. 确保Microsoft Outlook已启动并正常运行
2. 双击程序图标或运行 `python main.py`
3. 程序界面将显示

### 界面介绍

程序界面分为三个主要区域：

#### 1. 邮件发送区域（顶部）
- **收件人邮箱输入框**: 输入目标用户的邮箱地址
- **发送按钮**: 点击发送邮件

#### 2. 发送历史区域（中部）
- 显示最近15次邮件发送记录
- 包含邮箱地址、发送时间和状态信息

#### 3. 状态栏（底部）
- 显示当前操作状态
- 显示程序运行状态

### 发送邮件步骤

1. **输入邮箱地址**
   - 在"收件人邮箱"输入框中输入目标邮箱
   - 程序会自动验证邮箱格式
   - 有效邮箱格式示例：`<EMAIL>`

2. **发送邮件**
   - 点击"发送"按钮或按回车键
   - 程序会显示"正在发送邮件..."状态
   - 等待发送完成

3. **查看结果**
   - 发送成功：显示成功提示，邮箱输入框自动清空
   - 发送失败：显示错误信息，可重新尝试

### 邮件内容说明

程序发送的邮件具有以下固定设置：

- **邮件主题**: "Notification for Betelligent account is activated"
- **邮件格式**: HTML格式，包含图片和样式
- **BCC地址**: "Kell yan <<EMAIL>>"
- **邮件内容**: 使用预设的HTML模板

### 历史记录功能

#### 查看历史记录
- 发送历史自动显示在界面下方的表格中
- 显示最近15次发送记录
- 记录包含：邮箱地址、发送时间、发送状态

#### 状态说明
- **成功**: 邮件发送成功（绿色背景）
- **失败**: 邮件发送失败（红色背景）
- **警告**: 发送过程中有警告（黄色背景）

#### 数据保存
- 历史记录自动保存到本地文件
- 程序重启后历史记录仍然可见
- 最多保存15条记录，超出部分自动删除

## 常见问题解决

### 1. 程序无法启动

**问题**: 双击程序无反应或出现错误

**解决方案**:
- 确保已安装Python 3.7+
- 检查是否安装了PyQt5/PyQt6和pywin32
- 以管理员身份运行程序

### 2. Outlook相关错误

**问题**: 提示"Outlook not available"

**解决方案**:
- 确保Microsoft Outlook已安装
- 启动Outlook并确保正常运行
- 检查Outlook是否已配置邮箱账户
- 重启Outlook后再试

### 3. 邮件发送失败

**问题**: 显示"邮件发送失败"

**可能原因及解决方案**:
- **网络问题**: 检查网络连接
- **Outlook配置**: 确认Outlook邮箱配置正确
- **权限问题**: 以管理员身份运行程序
- **邮箱地址错误**: 检查收件人邮箱格式

### 4. HTML文件相关错误

**问题**: 提示"HTML文件未找到"

**解决方案**:
- 确保程序目录下存在HTML模板文件
- 检查文件名是否正确
- 确保依赖文件目录完整

### 5. 界面显示问题

**问题**: 界面显示不正常或字体模糊

**解决方案**:
- 调整Windows显示缩放设置
- 更新显卡驱动程序
- 重启程序

## 日志和调试

### 日志文件位置
程序运行时会自动生成日志文件：
- 位置：`data/logs/email_sender_YYYYMMDD.log`
- 格式：按日期命名，每天一个文件

### 日志内容
日志记录包含：
- 程序启动和关闭信息
- 邮件发送操作记录
- 错误和警告信息
- 文件操作记录

### 查看日志
当遇到问题时，可以查看日志文件获取详细信息：
1. 打开 `data/logs` 目录
2. 找到当天的日志文件
3. 用文本编辑器打开查看

## 安全注意事项

### 数据安全
- 历史记录仅保存在本地计算机
- 不会上传任何敏感信息到网络
- 邮箱地址信息仅用于发送邮件

### 使用权限
- 程序需要访问Outlook的权限
- 首次运行时Outlook可能会询问是否允许访问
- 请选择"允许"以确保程序正常工作

### 邮件安全
- BCC地址已固定设置，确保邮件副本发送到指定地址
- 邮件内容使用预设模板，避免内容篡改
- 建议在发送重要邮件前先进行测试

## 技术支持

### 获取帮助
如遇到技术问题，请：
1. 首先查看本手册的常见问题部分
2. 检查日志文件获取详细错误信息
3. 联系技术支持团队

### 反馈建议
如有功能建议或改进意见，欢迎反馈给开发团队。

### 版本更新
- 程序会不定期更新以修复问题和添加功能
- 更新时请备份历史记录数据
- 关注版本更新通知

## 附录

### 支持的邮箱格式
- 标准格式：`<EMAIL>`
- 支持各种域名：`.com`, `.org`, `.net`, `.edu` 等
- 支持子域名：`<EMAIL>`

### 快捷键
- **回车键**: 发送邮件（在邮箱输入框中）
- **Alt+F4**: 关闭程序

### 文件结构
```
程序目录/
├── main.py                                    # 主程序
├── gui/                                       # 界面模块
├── utils/                                     # 工具模块
├── data/                                      # 数据目录
│   ├── logs/                                  # 日志文件
│   └── send_history.json                      # 历史记录
├── Notification for Betelligent account is activated.htm    # 邮件模板
└── Notification for Betelligent account is activated.files/ # 模板依赖
```

---

**版本**: 1.0.0  
**更新日期**: 2024年1月  
**适用系统**: Windows 7/8/10/11
