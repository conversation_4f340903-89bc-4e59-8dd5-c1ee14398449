"""
文件处理模块

处理HTML文件读取、依赖文件管理等功能。
支持HTML文件解析和相关资源文件的处理。
"""

import os
import re
from typing import List, Dict, Optional, Tuple
from .logger import get_logger, log_file_operation
from .config import get_html_file_path, get_dependencies_dir_path


def validate_file_exists(file_path: str) -> bool:
    """
    验证文件是否存在
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件是否存在
    """
    exists = os.path.exists(file_path) and os.path.isfile(file_path)
    
    if not exists:
        log_file_operation(
            operation="validate_file",
            file_path=file_path,
            status="error",
            message="File does not exist"
        )
    
    return exists


def read_html_file(file_path: Optional[str] = None) -> Optional[str]:
    """
    读取HTML文件内容
    
    Args:
        file_path: HTML文件路径，如果为None则使用默认路径
        
    Returns:
        HTML文件内容，读取失败返回None
    """
    if file_path is None:
        file_path = get_html_file_path()
    
    logger = get_logger()
    
    if not validate_file_exists(file_path):
        return None
    
    try:
        # 尝试不同的编码方式读取文件
        encodings = ['utf-8', 'utf-16', 'gbk', 'cp1252']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                
                log_file_operation(
                    operation="read_html",
                    file_path=file_path,
                    status="success",
                    message=f"Read with encoding: {encoding}"
                )
                return content
                
            except UnicodeDecodeError:
                continue
        
        # 如果所有编码都失败，尝试二进制读取
        with open(file_path, 'rb') as f:
            content = f.read().decode('utf-8', errors='ignore')
        
        log_file_operation(
            operation="read_html",
            file_path=file_path,
            status="warning",
            message="Read with error handling"
        )
        return content
        
    except Exception as e:
        log_file_operation(
            operation="read_html",
            file_path=file_path,
            status="error",
            message=str(e)
        )
        return None


def get_dependency_files(dependencies_dir: Optional[str] = None) -> List[str]:
    """
    获取依赖文件列表
    
    Args:
        dependencies_dir: 依赖文件目录，如果为None则使用默认路径
        
    Returns:
        依赖文件路径列表
    """
    if dependencies_dir is None:
        dependencies_dir = get_dependencies_dir_path()
    
    logger = get_logger()
    dependency_files = []
    
    if not os.path.exists(dependencies_dir):
        log_file_operation(
            operation="get_dependencies",
            file_path=dependencies_dir,
            status="error",
            message="Dependencies directory does not exist"
        )
        return dependency_files
    
    try:
        for root, dirs, files in os.walk(dependencies_dir):
            for file in files:
                file_path = os.path.join(root, file)
                dependency_files.append(file_path)
        
        log_file_operation(
            operation="get_dependencies",
            file_path=dependencies_dir,
            status="success",
            message=f"Found {len(dependency_files)} dependency files"
        )
        
    except Exception as e:
        log_file_operation(
            operation="get_dependencies",
            file_path=dependencies_dir,
            status="error",
            message=str(e)
        )
    
    return dependency_files


def parse_html_resources(html_content: str) -> List[str]:
    """
    解析HTML内容中引用的资源文件
    
    Args:
        html_content: HTML内容
        
    Returns:
        资源文件路径列表
    """
    logger = get_logger()
    resources = []
    
    try:
        # 匹配各种资源引用模式
        patterns = [
            r'href\s*=\s*["\']([^"\']+)["\']',  # CSS, 链接等
            r'src\s*=\s*["\']([^"\']+)["\']',   # 图片, 脚本等
            r'url\s*\(\s*["\']?([^"\')\s]+)["\']?\s*\)',  # CSS中的url()
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                # 过滤掉HTTP链接和绝对路径
                if not match.startswith(('http://', 'https://', '//', '#')):
                    resources.append(match)
        
        # 去重
        resources = list(set(resources))
        
        log_file_operation(
            operation="parse_html_resources",
            file_path="html_content",
            status="success",
            message=f"Found {len(resources)} resource references"
        )
        
    except Exception as e:
        log_file_operation(
            operation="parse_html_resources",
            file_path="html_content",
            status="error",
            message=str(e)
        )
    
    return resources


def get_file_info(file_path: str) -> Dict[str, any]:
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件信息字典
    """
    info = {
        "path": file_path,
        "exists": False,
        "size": 0,
        "name": "",
        "extension": "",
        "directory": ""
    }
    
    try:
        if os.path.exists(file_path):
            info["exists"] = True
            info["size"] = os.path.getsize(file_path)
            info["name"] = os.path.basename(file_path)
            info["extension"] = os.path.splitext(file_path)[1]
            info["directory"] = os.path.dirname(file_path)
            
    except Exception as e:
        log_file_operation(
            operation="get_file_info",
            file_path=file_path,
            status="error",
            message=str(e)
        )
    
    return info


def prepare_email_attachments() -> Tuple[str, List[str]]:
    """
    准备邮件附件，包括HTML文件和依赖文件
    
    Returns:
        (HTML内容, 依赖文件路径列表)
    """
    logger = get_logger()
    
    # 读取HTML文件
    html_content = read_html_file()
    if html_content is None:
        logger.error("Failed to read HTML file")
        return "", []
    
    # 获取依赖文件
    dependency_files = get_dependency_files()
    
    # 验证依赖文件是否存在
    valid_dependencies = []
    for file_path in dependency_files:
        if validate_file_exists(file_path):
            valid_dependencies.append(file_path)
        else:
            logger.warning(f"Dependency file not found: {file_path}")
    
    log_file_operation(
        operation="prepare_attachments",
        file_path="email_preparation",
        status="success",
        message=f"HTML content ready, {len(valid_dependencies)} valid dependencies"
    )
    
    return html_content, valid_dependencies


def get_absolute_path(relative_path: str, base_dir: str = ".") -> str:
    """
    将相对路径转换为绝对路径
    
    Args:
        relative_path: 相对路径
        base_dir: 基础目录
        
    Returns:
        绝对路径
    """
    if os.path.isabs(relative_path):
        return relative_path
    
    return os.path.abspath(os.path.join(base_dir, relative_path))


def ensure_directory_exists(directory: str) -> bool:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        directory: 目录路径
        
    Returns:
        是否成功创建或已存在
    """
    try:
        os.makedirs(directory, exist_ok=True)
        return True
    except Exception as e:
        log_file_operation(
            operation="ensure_directory",
            file_path=directory,
            status="error",
            message=str(e)
        )
        return False
