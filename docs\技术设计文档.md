# Betelligent邮件发送工具 - 技术设计文档

## 1. 项目概述

### 1.1 项目背景
为Betelligent系统用户维护完成后发送通知邮件而开发的PyQt GUI工具。

### 1.2 主要功能
- 通过Outlook COM发送HTML格式的通知邮件
- 支持HTML文件及其依赖资源的处理
- 提供用户友好的图形界面
- 记录发送历史和操作日志
- 固定邮件主题和BCC设置

## 2. 系统架构

### 2.1 模块结构
```
sendemail/
├── main.py                 # 主程序入口
├── gui/                    # GUI界面模块
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   └── components.py       # UI组件
├── utils/                  # 工具函数模块
│   ├── __init__.py
│   ├── email_sender.py     # 邮件发送
│   ├── file_handler.py     # 文件处理
│   ├── logger.py           # 日志系统
│   └── config.py           # 配置管理
├── data/                   # 数据存储
│   ├── send_history.json   # 发送历史
│   └── logs/               # 日志文件
└── templates/              # 邮件模板
```

### 2.2 技术栈
- **GUI框架**: PyQt5/6
- **邮件发送**: Outlook COM (win32com.client)
- **文件处理**: Python标准库
- **日志系统**: Python logging模块
- **数据存储**: JSON文件

## 3. 详细设计

### 3.1 GUI界面设计

#### 主窗口布局
```
┌─────────────────────────────────────────┐
│ Betelligent邮件发送工具                    │
├─────────────────────────────────────────┤
│ 收件人邮箱: [___________________] [发送] │
│                                         │
│ 发送历史记录:                            │
│ ┌─────────────────────────────────────┐ │
│ │ 邮箱地址          │ 发送时间        │ │
│ │ <EMAIL> │ 2024-01-15 10:30│ │
│ │ <EMAIL> │ 2024-01-15 09:15│ │
│ │ ...               │ ...             │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ 状态栏: [准备就绪]                       │
└─────────────────────────────────────────┘
```

#### 界面组件规格
- **邮箱输入框**: QLineEdit，支持邮箱格式验证
- **发送按钮**: QPushButton，点击触发邮件发送
- **历史记录表**: QTableWidget，显示最近15条记录
- **状态栏**: QStatusBar，显示当前操作状态

### 3.2 邮件发送模块

#### 邮件配置
- **主题**: "Notification for Betelligent account is activated"
- **BCC**: "Kell yan <<EMAIL>>"
- **格式**: HTML格式
- **附件**: HTML文件及其依赖资源

#### 发送流程
1. 验证收件人邮箱格式
2. 读取HTML模板文件
3. 处理依赖资源文件
4. 创建Outlook邮件对象
5. 设置邮件属性（收件人、主题、BCC等）
6. 添加HTML内容和附件
7. 发送邮件
8. 记录发送结果

### 3.3 文件处理模块

#### HTML文件处理
- 读取主HTML文件内容
- 解析HTML中的资源引用
- 处理相对路径转换
- 确保资源文件完整性

#### 依赖资源处理
- 图片文件（PNG, GIF, JPG等）
- 样式文件（CSS, XML等）
- 其他支持文件（THMX等）

### 3.4 日志系统

#### 日志级别
- **INFO**: 正常操作记录
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **DEBUG**: 调试信息

#### 日志内容
- 操作时间戳
- 操作类型（发送邮件、文件处理等）
- 用户邮箱地址
- 操作结果
- 错误详情（如有）

### 3.5 历史记录管理

#### 数据结构
```json
{
  "send_history": [
    {
      "email": "<EMAIL>",
      "timestamp": "2024-01-15T10:30:00",
      "status": "success",
      "message": "邮件发送成功"
    }
  ]
}
```

#### 功能特性
- 最多保存15条记录
- 按时间倒序显示
- 支持状态筛选
- 自动清理过期记录

## 4. 错误处理

### 4.1 常见错误类型
- Outlook未安装或未启动
- 邮箱地址格式错误
- HTML文件或依赖文件缺失
- 网络连接问题
- 权限不足

### 4.2 错误处理策略
- 友好的错误提示信息
- 详细的日志记录
- 自动重试机制（适用场景）
- 用户操作指导

## 5. 性能优化

### 5.1 启动优化
- 延迟加载非关键模块
- 缓存常用配置
- 异步初始化

### 5.2 运行优化
- 邮件发送异步处理
- 文件读取缓存
- UI响应性保证

## 6. 安全考虑

### 6.1 数据安全
- 敏感信息不记录到日志
- 历史记录本地存储
- 文件访问权限控制

### 6.2 邮件安全
- 邮箱地址格式验证
- 防止邮件内容注入
- BCC地址固定保护

## 7. 部署说明

### 7.1 环境要求
- Windows操作系统
- Python 3.7+
- Microsoft Outlook已安装
- PyQt5/6库

### 7.2 安装步骤
1. 解压程序包到目标目录
2. 安装Python依赖包
3. 确保Outlook正常运行
4. 运行main.py启动程序

## 8. 维护和扩展

### 8.1 日常维护
- 定期清理日志文件
- 检查依赖包更新
- 监控错误日志

### 8.2 功能扩展
- 支持多种邮件模板
- 批量发送功能
- 邮件发送统计
- 配置文件管理界面
