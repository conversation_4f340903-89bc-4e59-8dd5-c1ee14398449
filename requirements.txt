# Betelligent邮件发送工具 - 依赖包列表

# GUI框架 (选择其中一个)
PyQt5>=5.15.0
# PyQt6>=6.0.0  # 可选，如果使用PyQt6请取消注释并注释PyQt5

# Windows COM接口
pywin32>=227

# 其他可能需要的包
# 注意：以下包通常随Python标准库提供，无需额外安装
# - json (标准库)
# - os (标准库) 
# - sys (标准库)
# - datetime (标准库)
# - re (标准库)
# - typing (Python 3.5+标准库)
# - pathlib (Python 3.4+标准库)
# - logging (标准库)

# 开发和测试工具 (可选)
# pytest>=6.0.0
# pytest-qt>=4.0.0
# black>=21.0.0
# flake8>=3.8.0
