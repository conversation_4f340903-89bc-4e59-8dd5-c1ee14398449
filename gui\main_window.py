"""
主窗口模块

实现应用程序的主界面，整合各个UI组件。
"""

import sys
import json
import os
from typing import Dict, Any, List
from datetime import datetime

try:
    from PyQt5.QtWidgets import (
        QMainWindow, QWidget, QVBoxLayout, QHB<PERSON>Layout, 
        QApplication, QMessageBox, QSplitter
    )
    from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
    from PyQt5.QtGui import QIcon, QFont
    PYQT_AVAILABLE = True
except ImportError:
    try:
        from PyQt6.QtWidgets import (
            QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
            QApplication, QMessageBox, QSplitter
        )
        from PyQt6.QtCore import Qt, pyqtSignal, QTimer
        from PyQt6.QtGui import QIcon, QFont
        PYQT_AVAILABLE = True
    except ImportError:
        PYQT_AVAILABLE = False

if PYQT_AVAILABLE:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from .components import EmailInputWidget, HistoryTableWidget, StatusBarWidget
    from utils.email_sender import send_email_via_outlook
    from utils.config import get_window_config, get_max_history_records, create_required_directories
    from utils.logger import get_logger, setup_logger


class EmailSendThread(QThread):
    """邮件发送线程"""

    # 信号定义
    send_finished = pyqtSignal(dict)  # 发送完成信号
    send_progress = pyqtSignal(str)   # 发送进度信号

    def __init__(self, email_address: str, bcc_address: str = "", bcc_enabled: bool = True):
        super().__init__()
        self.email_address = email_address
        self.bcc_address = bcc_address if bcc_enabled else ""
        self.bcc_enabled = bcc_enabled
        self.logger = get_logger()
    
    def run(self):
        """线程执行函数"""
        try:
            self.send_progress.emit("正在准备邮件内容...")

            # 发送邮件，传递BCC配置
            result = send_email_via_outlook(
                to_address=self.email_address,
                bcc_address=self.bcc_address if self.bcc_enabled else None
            )

            self.send_finished.emit(result)

        except Exception as e:
            error_result = {
                "success": False,
                "message": f"发送过程中出现异常: {str(e)}",
                "details": {"to_address": self.email_address}
            }
            self.send_finished.emit(error_result)


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger()
        self.send_thread = None
        self.history_data = []
        
        # 创建必需的目录
        create_required_directories()
        
        self.setup_ui()
        self.setup_connections()
        self.load_history()
        
        self.logger.info("Main window initialized")
    
    def setup_ui(self):
        """设置用户界面"""
        # 设置窗口属性
        window_config = get_window_config()
        self.setWindowTitle(window_config["title"])
        self.setGeometry(100, 100, window_config["width"], window_config["height"])
        
        # 设置窗口图标（如果有的话）
        # self.setWindowIcon(QIcon("icon.ico"))
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(20, 20, 20, 10)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        
        # 邮件输入组件
        self.email_input_widget = EmailInputWidget()
        splitter.addWidget(self.email_input_widget)
        
        # 历史记录组件
        self.history_widget = HistoryTableWidget()
        splitter.addWidget(self.history_widget)
        
        # 设置分割器比例
        splitter.setSizes([150, 350])  # 输入区域较小，历史记录区域较大
        
        main_layout.addWidget(splitter)
        
        # 状态栏
        self.status_bar = StatusBarWidget()
        self.setStatusBar(self.status_bar)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QWidget {
                font-family: "Microsoft YaHei", "SimHei", sans-serif;
                font-size: 10pt;
            }
        """)
    
    def setup_connections(self):
        """设置信号连接"""
        self.email_input_widget.email_send_requested.connect(self.on_send_email)
    
    def on_send_email(self, email_address: str, bcc_address: str, bcc_enabled: bool):
        """处理发送邮件请求"""
        bcc_info = f" (BCC: {bcc_address})" if bcc_enabled and bcc_address else " (无BCC)"
        self.logger.info(f"Send email requested for: {email_address}{bcc_info}")

        # 禁用输入组件
        self.email_input_widget.set_enabled(False)

        # 显示进度状态
        self.status_bar.show_progress("正在发送邮件...")

        # 创建并启动发送线程
        self.send_thread = EmailSendThread(email_address, bcc_address, bcc_enabled)
        self.send_thread.send_progress.connect(self.status_bar.show_progress)
        self.send_thread.send_finished.connect(self.on_send_finished)
        self.send_thread.start()
    
    def on_send_finished(self, result: Dict[str, Any]):
        """处理邮件发送完成"""
        email_address = result["details"]["to_address"]
        
        # 重新启用输入组件
        self.email_input_widget.set_enabled(True)
        
        if result["success"]:
            # 发送成功
            self.status_bar.show_success("邮件发送成功")
            self.email_input_widget.clear_input()
            
            # 显示成功消息
            QMessageBox.information(
                self, 
                "发送成功", 
                f"邮件已成功发送到 {email_address}"
            )
            
            # 添加到历史记录
            self.add_history_record(email_address, "success", "邮件发送成功")
            
        else:
            # 发送失败
            self.status_bar.show_error("邮件发送失败")
            
            # 显示错误消息
            QMessageBox.critical(
                self, 
                "发送失败", 
                f"邮件发送失败:\n{result['message']}"
            )
            
            # 添加到历史记录
            self.add_history_record(email_address, "error", result["message"])
        
        # 清理线程
        if self.send_thread:
            self.send_thread.deleteLater()
            self.send_thread = None
    
    def add_history_record(self, email: str, status: str, message: str):
        """添加历史记录"""
        record = {
            "email": email,
            "timestamp": datetime.now().isoformat(),
            "status": status,
            "message": message
        }
        
        # 添加到历史数据
        self.history_data.insert(0, record)  # 插入到开头
        
        # 限制记录数量
        max_records = get_max_history_records()
        if len(self.history_data) > max_records:
            self.history_data = self.history_data[:max_records]
        
        # 更新界面
        self.history_widget.load_history(self.history_data)
        
        # 保存到文件
        self.save_history()
    
    def load_history(self):
        """加载历史记录"""
        history_file = "data/send_history.json"
        
        try:
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.history_data = data.get("send_history", [])
                
                # 更新界面
                self.history_widget.load_history(self.history_data)
                self.logger.info(f"Loaded {len(self.history_data)} history records")
            else:
                self.logger.info("No history file found, starting with empty history")
                
        except Exception as e:
            self.logger.error(f"Error loading history: {str(e)}")
            self.history_data = []
    
    def save_history(self):
        """保存历史记录"""
        history_file = "data/send_history.json"
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(history_file), exist_ok=True)
            
            data = {"send_history": self.history_data}
            
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug("History saved successfully")
            
        except Exception as e:
            self.logger.error(f"Error saving history: {str(e)}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 如果有正在进行的发送线程，等待其完成
        if self.send_thread and self.send_thread.isRunning():
            reply = QMessageBox.question(
                self,
                "确认退出",
                "邮件正在发送中，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                event.ignore()
                return
            
            # 强制终止线程
            self.send_thread.terminate()
            self.send_thread.wait()
        
        # 保存历史记录
        self.save_history()
        
        self.logger.info("Application closing")
        event.accept()


def create_application() -> QApplication:
    """创建QApplication实例"""
    app = QApplication(sys.argv)
    app.setApplicationName("Betelligent邮件发送工具")
    app.setApplicationVersion("1.0.0")
    
    # 设置应用程序样式
    app.setStyle("Fusion")
    
    return app


def run_application():
    """运行应用程序"""
    if not PYQT_AVAILABLE:
        print("错误: 未找到PyQt5或PyQt6库，请先安装:")
        print("pip install PyQt5 或 pip install PyQt6")
        return
    
    app = create_application()
    
    try:
        window = MainWindow()
        window.show()
        
        sys.exit(app.exec_())
        
    except Exception as e:
        logger = get_logger()
        logger.error(f"Application error: {str(e)}")
        QMessageBox.critical(None, "应用程序错误", f"应用程序启动失败:\n{str(e)}")
        sys.exit(1)
